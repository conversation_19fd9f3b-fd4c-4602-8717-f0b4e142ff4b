import sys
import os

print("测试可执行文件...")
print(f"Python版本: {sys.version}")
print(f"当前工作目录: {os.getcwd()}")

try:
    import openpyxl
    print("✅ openpyxl 导入成功")
except ImportError as e:
    print(f"❌ openpyxl 导入失败: {e}")

try:
    import tkinter
    print("✅ tkinter 导入成功")
except ImportError as e:
    print(f"❌ tkinter 导入失败: {e}")

try:
    import csv
    print("✅ csv 导入成功")
except ImportError as e:
    print(f"❌ csv 导入失败: {e}")

print("测试完成！")
input("按回车键退出...")
