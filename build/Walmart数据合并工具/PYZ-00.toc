('C:\\Users\\<USER>\\Desktop\\公司脚本\\weicheng\\build\\Walmart数据合并工具\\PYZ-00.pyz',
 [('PIL',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.AvifImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\AvifImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._typing',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._util',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('__future__', 'D:\\huanjing\\Python3.11.2\\Lib\\__future__.py', 'PYMODULE'),
  ('_aix_support',
   'D:\\huanjing\\Python3.11.2\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   'D:\\huanjing\\Python3.11.2\\Lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('_compat_pickle',
   'D:\\huanjing\\Python3.11.2\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'D:\\huanjing\\Python3.11.2\\Lib\\_compression.py',
   'PYMODULE'),
  ('_py_abc', 'D:\\huanjing\\Python3.11.2\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\huanjing\\Python3.11.2\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_strptime', 'D:\\huanjing\\Python3.11.2\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'D:\\huanjing\\Python3.11.2\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse', 'D:\\huanjing\\Python3.11.2\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\huanjing\\Python3.11.2\\Lib\\ast.py', 'PYMODULE'),
  ('asyncio',
   'D:\\huanjing\\Python3.11.2\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'D:\\huanjing\\Python3.11.2\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\huanjing\\Python3.11.2\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\huanjing\\Python3.11.2\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\huanjing\\Python3.11.2\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\huanjing\\Python3.11.2\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\huanjing\\Python3.11.2\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'D:\\huanjing\\Python3.11.2\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\huanjing\\Python3.11.2\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\huanjing\\Python3.11.2\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'D:\\huanjing\\Python3.11.2\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'D:\\huanjing\\Python3.11.2\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'D:\\huanjing\\Python3.11.2\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'D:\\huanjing\\Python3.11.2\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\huanjing\\Python3.11.2\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\huanjing\\Python3.11.2\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'D:\\huanjing\\Python3.11.2\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'D:\\huanjing\\Python3.11.2\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\huanjing\\Python3.11.2\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'D:\\huanjing\\Python3.11.2\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'D:\\huanjing\\Python3.11.2\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'D:\\huanjing\\Python3.11.2\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\huanjing\\Python3.11.2\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'D:\\huanjing\\Python3.11.2\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'D:\\huanjing\\Python3.11.2\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'D:\\huanjing\\Python3.11.2\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'D:\\huanjing\\Python3.11.2\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   'D:\\huanjing\\Python3.11.2\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'D:\\huanjing\\Python3.11.2\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\huanjing\\Python3.11.2\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\huanjing\\Python3.11.2\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\huanjing\\Python3.11.2\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('base64', 'D:\\huanjing\\Python3.11.2\\Lib\\base64.py', 'PYMODULE'),
  ('bdb', 'D:\\huanjing\\Python3.11.2\\Lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'D:\\huanjing\\Python3.11.2\\Lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'D:\\huanjing\\Python3.11.2\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'D:\\huanjing\\Python3.11.2\\Lib\\calendar.py', 'PYMODULE'),
  ('cgi', 'D:\\huanjing\\Python3.11.2\\Lib\\cgi.py', 'PYMODULE'),
  ('charset_normalizer',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('cmd', 'D:\\huanjing\\Python3.11.2\\Lib\\cmd.py', 'PYMODULE'),
  ('code', 'D:\\huanjing\\Python3.11.2\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\huanjing\\Python3.11.2\\Lib\\codeop.py', 'PYMODULE'),
  ('colorsys', 'D:\\huanjing\\Python3.11.2\\Lib\\colorsys.py', 'PYMODULE'),
  ('concurrent',
   'D:\\huanjing\\Python3.11.2\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\huanjing\\Python3.11.2\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\huanjing\\Python3.11.2\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\huanjing\\Python3.11.2\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\huanjing\\Python3.11.2\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('contextlib', 'D:\\huanjing\\Python3.11.2\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars',
   'D:\\huanjing\\Python3.11.2\\Lib\\contextvars.py',
   'PYMODULE'),
  ('copy', 'D:\\huanjing\\Python3.11.2\\Lib\\copy.py', 'PYMODULE'),
  ('cssselect',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\cssselect\\__init__.py',
   'PYMODULE'),
  ('cssselect.parser',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\cssselect\\parser.py',
   'PYMODULE'),
  ('cssselect.xpath',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\cssselect\\xpath.py',
   'PYMODULE'),
  ('csv', 'D:\\huanjing\\Python3.11.2\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes',
   'D:\\huanjing\\Python3.11.2\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'D:\\huanjing\\Python3.11.2\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('dataclasses',
   'D:\\huanjing\\Python3.11.2\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime', 'D:\\huanjing\\Python3.11.2\\Lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'D:\\huanjing\\Python3.11.2\\Lib\\decimal.py', 'PYMODULE'),
  ('defusedxml',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\defusedxml\\__init__.py',
   'PYMODULE'),
  ('defusedxml.ElementTree',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\defusedxml\\ElementTree.py',
   'PYMODULE'),
  ('defusedxml.cElementTree',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\defusedxml\\cElementTree.py',
   'PYMODULE'),
  ('defusedxml.common',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\defusedxml\\common.py',
   'PYMODULE'),
  ('defusedxml.expatbuilder',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\defusedxml\\expatbuilder.py',
   'PYMODULE'),
  ('defusedxml.expatreader',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\defusedxml\\expatreader.py',
   'PYMODULE'),
  ('defusedxml.minidom',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\defusedxml\\minidom.py',
   'PYMODULE'),
  ('defusedxml.pulldom',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\defusedxml\\pulldom.py',
   'PYMODULE'),
  ('defusedxml.sax',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\defusedxml\\sax.py',
   'PYMODULE'),
  ('defusedxml.xmlrpc',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\defusedxml\\xmlrpc.py',
   'PYMODULE'),
  ('difflib', 'D:\\huanjing\\Python3.11.2\\Lib\\difflib.py', 'PYMODULE'),
  ('dis', 'D:\\huanjing\\Python3.11.2\\Lib\\dis.py', 'PYMODULE'),
  ('doctest', 'D:\\huanjing\\Python3.11.2\\Lib\\doctest.py', 'PYMODULE'),
  ('email', 'D:\\huanjing\\Python3.11.2\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\huanjing\\Python3.11.2\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\huanjing\\Python3.11.2\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\huanjing\\Python3.11.2\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\huanjing\\Python3.11.2\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\huanjing\\Python3.11.2\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'D:\\huanjing\\Python3.11.2\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\huanjing\\Python3.11.2\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\huanjing\\Python3.11.2\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\huanjing\\Python3.11.2\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\huanjing\\Python3.11.2\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\huanjing\\Python3.11.2\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'D:\\huanjing\\Python3.11.2\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\huanjing\\Python3.11.2\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\huanjing\\Python3.11.2\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'D:\\huanjing\\Python3.11.2\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'D:\\huanjing\\Python3.11.2\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\huanjing\\Python3.11.2\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\huanjing\\Python3.11.2\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'D:\\huanjing\\Python3.11.2\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('et_xmlfile',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('et_xmlfile.incremental_tree',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('fileinput', 'D:\\huanjing\\Python3.11.2\\Lib\\fileinput.py', 'PYMODULE'),
  ('fnmatch', 'D:\\huanjing\\Python3.11.2\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'D:\\huanjing\\Python3.11.2\\Lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'D:\\huanjing\\Python3.11.2\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'D:\\huanjing\\Python3.11.2\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'D:\\huanjing\\Python3.11.2\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'D:\\huanjing\\Python3.11.2\\Lib\\gettext.py', 'PYMODULE'),
  ('glob', 'D:\\huanjing\\Python3.11.2\\Lib\\glob.py', 'PYMODULE'),
  ('gzip', 'D:\\huanjing\\Python3.11.2\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'D:\\huanjing\\Python3.11.2\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'D:\\huanjing\\Python3.11.2\\Lib\\hmac.py', 'PYMODULE'),
  ('html', 'D:\\huanjing\\Python3.11.2\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'D:\\huanjing\\Python3.11.2\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('http', 'D:\\huanjing\\Python3.11.2\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client',
   'D:\\huanjing\\Python3.11.2\\Lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'D:\\huanjing\\Python3.11.2\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.server',
   'D:\\huanjing\\Python3.11.2\\Lib\\http\\server.py',
   'PYMODULE'),
  ('importlib',
   'D:\\huanjing\\Python3.11.2\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'D:\\huanjing\\Python3.11.2\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\huanjing\\Python3.11.2\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\huanjing\\Python3.11.2\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\huanjing\\Python3.11.2\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'D:\\huanjing\\Python3.11.2\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\huanjing\\Python3.11.2\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\huanjing\\Python3.11.2\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\huanjing\\Python3.11.2\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\huanjing\\Python3.11.2\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\huanjing\\Python3.11.2\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\huanjing\\Python3.11.2\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\huanjing\\Python3.11.2\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'D:\\huanjing\\Python3.11.2\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\huanjing\\Python3.11.2\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\huanjing\\Python3.11.2\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\huanjing\\Python3.11.2\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\huanjing\\Python3.11.2\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'D:\\huanjing\\Python3.11.2\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\huanjing\\Python3.11.2\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\huanjing\\Python3.11.2\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'D:\\huanjing\\Python3.11.2\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'D:\\huanjing\\Python3.11.2\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'D:\\huanjing\\Python3.11.2\\Lib\\ipaddress.py', 'PYMODULE'),
  ('json', 'D:\\huanjing\\Python3.11.2\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder',
   'D:\\huanjing\\Python3.11.2\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'D:\\huanjing\\Python3.11.2\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\huanjing\\Python3.11.2\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'D:\\huanjing\\Python3.11.2\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lxml',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\lxml\\__init__.py',
   'PYMODULE'),
  ('lxml.ElementInclude',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\lxml\\ElementInclude.py',
   'PYMODULE'),
  ('lxml.cssselect',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\lxml\\cssselect.py',
   'PYMODULE'),
  ('lxml.doctestcompare',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\lxml\\doctestcompare.py',
   'PYMODULE'),
  ('lxml.html',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\lxml\\html\\__init__.py',
   'PYMODULE'),
  ('lxml.html.ElementSoup',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\lxml\\html\\ElementSoup.py',
   'PYMODULE'),
  ('lxml.html._diffcommand',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\lxml\\html\\_diffcommand.py',
   'PYMODULE'),
  ('lxml.html._html5builder',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\lxml\\html\\_html5builder.py',
   'PYMODULE'),
  ('lxml.html._setmixin',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\lxml\\html\\_setmixin.py',
   'PYMODULE'),
  ('lxml.html.builder',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\lxml\\html\\builder.py',
   'PYMODULE'),
  ('lxml.html.clean',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\lxml\\html\\clean.py',
   'PYMODULE'),
  ('lxml.html.defs',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\lxml\\html\\defs.py',
   'PYMODULE'),
  ('lxml.html.formfill',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\lxml\\html\\formfill.py',
   'PYMODULE'),
  ('lxml.html.html5parser',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\lxml\\html\\html5parser.py',
   'PYMODULE'),
  ('lxml.html.soupparser',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\lxml\\html\\soupparser.py',
   'PYMODULE'),
  ('lxml.html.usedoctest',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\lxml\\html\\usedoctest.py',
   'PYMODULE'),
  ('lxml.includes',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\lxml\\includes\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.extlibs',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\lxml\\includes\\extlibs\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libexslt',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\lxml\\includes\\libexslt\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxml',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\lxml\\includes\\libxml\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxslt',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\lxml\\includes\\libxslt\\__init__.py',
   'PYMODULE'),
  ('lxml.isoschematron',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\lxml\\isoschematron\\__init__.py',
   'PYMODULE'),
  ('lxml.pyclasslookup',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\lxml\\pyclasslookup.py',
   'PYMODULE'),
  ('lxml.usedoctest',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\lxml\\usedoctest.py',
   'PYMODULE'),
  ('lzma', 'D:\\huanjing\\Python3.11.2\\Lib\\lzma.py', 'PYMODULE'),
  ('mimetypes', 'D:\\huanjing\\Python3.11.2\\Lib\\mimetypes.py', 'PYMODULE'),
  ('multiprocessing',
   'D:\\huanjing\\Python3.11.2\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\huanjing\\Python3.11.2\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\huanjing\\Python3.11.2\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\huanjing\\Python3.11.2\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\huanjing\\Python3.11.2\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\huanjing\\Python3.11.2\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\huanjing\\Python3.11.2\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\huanjing\\Python3.11.2\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\huanjing\\Python3.11.2\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\huanjing\\Python3.11.2\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\huanjing\\Python3.11.2\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\huanjing\\Python3.11.2\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\huanjing\\Python3.11.2\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\huanjing\\Python3.11.2\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\huanjing\\Python3.11.2\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\huanjing\\Python3.11.2\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\huanjing\\Python3.11.2\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\huanjing\\Python3.11.2\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\huanjing\\Python3.11.2\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\huanjing\\Python3.11.2\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\huanjing\\Python3.11.2\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\huanjing\\Python3.11.2\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\huanjing\\Python3.11.2\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'D:\\huanjing\\Python3.11.2\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'D:\\huanjing\\Python3.11.2\\Lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'D:\\huanjing\\Python3.11.2\\Lib\\numbers.py', 'PYMODULE'),
  ('numpy',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy._core',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.records',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.tests', '-', 'PYMODULE'),
  ('numpy._core.tests._natype',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_core\\tests\\_natype.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._utils',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.char',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy.core',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.f2py',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.fft',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.linalg',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matlib',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.rec',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy.testing',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode', 'D:\\huanjing\\Python3.11.2\\Lib\\opcode.py', 'PYMODULE'),
  ('openpyxl',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.descriptors.container',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\descriptors\\container.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('optparse', 'D:\\huanjing\\Python3.11.2\\Lib\\optparse.py', 'PYMODULE'),
  ('pathlib', 'D:\\huanjing\\Python3.11.2\\Lib\\pathlib.py', 'PYMODULE'),
  ('pdb', 'D:\\huanjing\\Python3.11.2\\Lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'D:\\huanjing\\Python3.11.2\\Lib\\pickle.py', 'PYMODULE'),
  ('pkgutil', 'D:\\huanjing\\Python3.11.2\\Lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'D:\\huanjing\\Python3.11.2\\Lib\\platform.py', 'PYMODULE'),
  ('pprint', 'D:\\huanjing\\Python3.11.2\\Lib\\pprint.py', 'PYMODULE'),
  ('psutil',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._common',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('py_compile', 'D:\\huanjing\\Python3.11.2\\Lib\\py_compile.py', 'PYMODULE'),
  ('pydoc', 'D:\\huanjing\\Python3.11.2\\Lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data',
   'D:\\huanjing\\Python3.11.2\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\huanjing\\Python3.11.2\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('queue', 'D:\\huanjing\\Python3.11.2\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'D:\\huanjing\\Python3.11.2\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\huanjing\\Python3.11.2\\Lib\\random.py', 'PYMODULE'),
  ('runpy', 'D:\\huanjing\\Python3.11.2\\Lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'D:\\huanjing\\Python3.11.2\\Lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'D:\\huanjing\\Python3.11.2\\Lib\\selectors.py', 'PYMODULE'),
  ('shlex', 'D:\\huanjing\\Python3.11.2\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'D:\\huanjing\\Python3.11.2\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\huanjing\\Python3.11.2\\Lib\\signal.py', 'PYMODULE'),
  ('socket', 'D:\\huanjing\\Python3.11.2\\Lib\\socket.py', 'PYMODULE'),
  ('socketserver',
   'D:\\huanjing\\Python3.11.2\\Lib\\socketserver.py',
   'PYMODULE'),
  ('ssl', 'D:\\huanjing\\Python3.11.2\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'D:\\huanjing\\Python3.11.2\\Lib\\statistics.py', 'PYMODULE'),
  ('string', 'D:\\huanjing\\Python3.11.2\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'D:\\huanjing\\Python3.11.2\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'D:\\huanjing\\Python3.11.2\\Lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'D:\\huanjing\\Python3.11.2\\Lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'D:\\huanjing\\Python3.11.2\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'D:\\huanjing\\Python3.11.2\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'D:\\huanjing\\Python3.11.2\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'D:\\huanjing\\Python3.11.2\\Lib\\threading.py', 'PYMODULE'),
  ('tkinter',
   'D:\\huanjing\\Python3.11.2\\Lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'D:\\huanjing\\Python3.11.2\\Lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'D:\\huanjing\\Python3.11.2\\Lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'D:\\huanjing\\Python3.11.2\\Lib\\tkinter\\dialog.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'D:\\huanjing\\Python3.11.2\\Lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'D:\\huanjing\\Python3.11.2\\Lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'D:\\huanjing\\Python3.11.2\\Lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('token', 'D:\\huanjing\\Python3.11.2\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\huanjing\\Python3.11.2\\Lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc',
   'D:\\huanjing\\Python3.11.2\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('tty', 'D:\\huanjing\\Python3.11.2\\Lib\\tty.py', 'PYMODULE'),
  ('typing', 'D:\\huanjing\\Python3.11.2\\Lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest',
   'D:\\huanjing\\Python3.11.2\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'D:\\huanjing\\Python3.11.2\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'D:\\huanjing\\Python3.11.2\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'D:\\huanjing\\Python3.11.2\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'D:\\huanjing\\Python3.11.2\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'D:\\huanjing\\Python3.11.2\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.result',
   'D:\\huanjing\\Python3.11.2\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'D:\\huanjing\\Python3.11.2\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'D:\\huanjing\\Python3.11.2\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'D:\\huanjing\\Python3.11.2\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'D:\\huanjing\\Python3.11.2\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'D:\\huanjing\\Python3.11.2\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'D:\\huanjing\\Python3.11.2\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'D:\\huanjing\\Python3.11.2\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'D:\\huanjing\\Python3.11.2\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'D:\\huanjing\\Python3.11.2\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('webbrowser', 'D:\\huanjing\\Python3.11.2\\Lib\\webbrowser.py', 'PYMODULE'),
  ('xml', 'D:\\huanjing\\Python3.11.2\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.dom',
   'D:\\huanjing\\Python3.11.2\\Lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'D:\\huanjing\\Python3.11.2\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'D:\\huanjing\\Python3.11.2\\Lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'D:\\huanjing\\Python3.11.2\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'D:\\huanjing\\Python3.11.2\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'D:\\huanjing\\Python3.11.2\\Lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'D:\\huanjing\\Python3.11.2\\Lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'D:\\huanjing\\Python3.11.2\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree',
   'D:\\huanjing\\Python3.11.2\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'D:\\huanjing\\Python3.11.2\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'D:\\huanjing\\Python3.11.2\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'D:\\huanjing\\Python3.11.2\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'D:\\huanjing\\Python3.11.2\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'D:\\huanjing\\Python3.11.2\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\huanjing\\Python3.11.2\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'D:\\huanjing\\Python3.11.2\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\huanjing\\Python3.11.2\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\huanjing\\Python3.11.2\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\huanjing\\Python3.11.2\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\huanjing\\Python3.11.2\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\huanjing\\Python3.11.2\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'D:\\huanjing\\Python3.11.2\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'D:\\huanjing\\Python3.11.2\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc.server',
   'D:\\huanjing\\Python3.11.2\\Lib\\xmlrpc\\server.py',
   'PYMODULE'),
  ('yaml',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\yaml\\__init__.py',
   'PYMODULE'),
  ('yaml.composer',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\yaml\\composer.py',
   'PYMODULE'),
  ('yaml.constructor',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\yaml\\constructor.py',
   'PYMODULE'),
  ('yaml.cyaml',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\yaml\\cyaml.py',
   'PYMODULE'),
  ('yaml.dumper',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\yaml\\dumper.py',
   'PYMODULE'),
  ('yaml.emitter',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\yaml\\emitter.py',
   'PYMODULE'),
  ('yaml.error',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\yaml\\error.py',
   'PYMODULE'),
  ('yaml.events',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\yaml\\events.py',
   'PYMODULE'),
  ('yaml.loader',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\yaml\\loader.py',
   'PYMODULE'),
  ('yaml.nodes',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\yaml\\nodes.py',
   'PYMODULE'),
  ('yaml.parser',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\yaml\\parser.py',
   'PYMODULE'),
  ('yaml.reader',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\yaml\\reader.py',
   'PYMODULE'),
  ('yaml.representer',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\yaml\\representer.py',
   'PYMODULE'),
  ('yaml.resolver',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\yaml\\resolver.py',
   'PYMODULE'),
  ('yaml.scanner',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\yaml\\scanner.py',
   'PYMODULE'),
  ('yaml.serializer',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\yaml\\serializer.py',
   'PYMODULE'),
  ('yaml.tokens',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\yaml\\tokens.py',
   'PYMODULE'),
  ('zipfile', 'D:\\huanjing\\Python3.11.2\\Lib\\zipfile.py', 'PYMODULE'),
  ('zipimport', 'D:\\huanjing\\Python3.11.2\\Lib\\zipimport.py', 'PYMODULE')])
