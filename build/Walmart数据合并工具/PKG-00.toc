('C:\\Users\\<USER>\\Desktop\\公司脚本\\weicheng\\build\\Walmart数据合并工具\\Walmart数据合并工具.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'C:\\Users\\<USER>\\Desktop\\公司脚本\\weicheng\\build\\Walmart数据合并工具\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'C:\\Users\\<USER>\\Desktop\\公司脚本\\weicheng\\build\\Walmart数据合并工具\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'C:\\Users\\<USER>\\Desktop\\公司脚本\\weicheng\\build\\Walmart数据合并工具\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'C:\\Users\\<USER>\\Desktop\\公司脚本\\weicheng\\build\\Walmart数据合并工具\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'C:\\Users\\<USER>\\Desktop\\公司脚本\\weicheng\\build\\Walmart数据合并工具\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'C:\\Users\\<USER>\\Desktop\\公司脚本\\weicheng\\build\\Walmart数据合并工具\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('merge_weekly_walmart_excel',
   'C:\\Users\\<USER>\\Desktop\\公司脚本\\weicheng\\merge_weekly_walmart_excel.py',
   'PYSOURCE'),
  ('python311.dll', 'D:\\huanjing\\Python3.11.2\\python311.dll', 'BINARY'),
  ('numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-43e11ff0749b8cbe0a615c9cf6737e0e.dll',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy.libs\\libscipy_openblas64_-43e11ff0749b8cbe0a615c9cf6737e0e.dll',
   'BINARY'),
  ('select.pyd', 'D:\\huanjing\\Python3.11.2\\DLLs\\select.pyd', 'EXTENSION'),
  ('_decimal.pyd',
   'D:\\huanjing\\Python3.11.2\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\huanjing\\Python3.11.2\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'D:\\huanjing\\Python3.11.2\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_socket.pyd', 'D:\\huanjing\\Python3.11.2\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'D:\\huanjing\\Python3.11.2\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\huanjing\\Python3.11.2\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\huanjing\\Python3.11.2\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'D:\\huanjing\\Python3.11.2\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'D:\\huanjing\\Python3.11.2\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'D:\\huanjing\\Python3.11.2\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_queue.pyd', 'D:\\huanjing\\Python3.11.2\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_tkinter.pyd',
   'D:\\huanjing\\Python3.11.2\\DLLs\\_tkinter.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'D:\\huanjing\\Python3.11.2\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('lxml\\etree.cp311-win_amd64.pyd',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\lxml\\etree.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\_elementpath.cp311-win_amd64.pyd',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\lxml\\_elementpath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\sax.cp311-win_amd64.pyd',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\lxml\\sax.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\objectify.cp311-win_amd64.pyd',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\lxml\\objectify.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\html\\diff.cp311-win_amd64.pyd',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\lxml\\html\\diff.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'D:\\huanjing\\Python3.11.2\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'D:\\huanjing\\Python3.11.2\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('lxml\\builder.cp311-win_amd64.pyd',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\lxml\\builder.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp311-win_amd64.pyd',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp311-win_amd64.pyd',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp311-win_amd64.pyd',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp311-win_amd64.pyd',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\charset_normalizer\\md.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp311-win_amd64.pyd',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp311-win_amd64.pyd',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\random\\mtrand.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp311-win_amd64.pyd',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\random\\_sfc64.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp311-win_amd64.pyd',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\random\\_philox.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp311-win_amd64.pyd',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\random\\_pcg64.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp311-win_amd64.pyd',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\random\\_mt19937.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp311-win_amd64.pyd',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\random\\bit_generator.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp311-win_amd64.pyd',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\random\\_generator.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp311-win_amd64.pyd',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp311-win_amd64.pyd',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\random\\_common.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp311-win_amd64.pyd',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('yaml\\_yaml.cp311-win_amd64.pyd',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\yaml\\_yaml.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp311-win_amd64.pyd',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\_webp.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp311-win_amd64.pyd',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\_imagingtk.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp311-win_amd64.pyd',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\_imagingcms.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp311-win_amd64.pyd',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\_imagingmath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp311-win_amd64.pyd',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\PIL\\_imaging.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'E:\\ruanjian\\Android2222\\jbr\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'E:\\ruanjian\\Android2222\\jbr\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'E:\\ruanjian\\Android2222\\jbr\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'E:\\ruanjian\\Android2222\\jbr\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'E:\\ruanjian\\Android2222\\jbr\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'E:\\ruanjian\\Android2222\\jbr\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'E:\\ruanjian\\Android2222\\jbr\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'E:\\ruanjian\\Android2222\\jbr\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'D:\\huanjing\\Python3.11.2\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'E:\\ruanjian\\Android2222\\jbr\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'E:\\ruanjian\\Android2222\\jbr\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'E:\\ruanjian\\Android2222\\jbr\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'E:\\ruanjian\\Android2222\\jbr\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'E:\\ruanjian\\Android2222\\jbr\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'D:\\huanjing\\Python3.11.2\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'E:\\ruanjian\\Android2222\\jbr\\bin\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'D:\\huanjing\\Python3.11.2\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libssl-1_1.dll',
   'D:\\huanjing\\Python3.11.2\\DLLs\\libssl-1_1.dll',
   'BINARY'),
  ('libffi-8.dll', 'D:\\huanjing\\Python3.11.2\\DLLs\\libffi-8.dll', 'BINARY'),
  ('tcl86t.dll', 'D:\\huanjing\\Python3.11.2\\DLLs\\tcl86t.dll', 'BINARY'),
  ('tk86t.dll', 'D:\\huanjing\\Python3.11.2\\DLLs\\tk86t.dll', 'BINARY'),
  ('python3.dll', 'D:\\huanjing\\Python3.11.2\\python3.dll', 'BINARY'),
  ('ucrtbase.dll',
   'E:\\ruanjian\\Android2222\\jbr\\bin\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'E:\\ruanjian\\Android2222\\jbr\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'E:\\ruanjian\\Android2222\\jbr\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'E:\\ruanjian\\Android2222\\jbr\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'E:\\ruanjian\\Android2222\\jbr\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'E:\\ruanjian\\Android2222\\jbr\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'E:\\ruanjian\\Android2222\\jbr\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'E:\\ruanjian\\Android2222\\jbr\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'E:\\ruanjian\\Android2222\\jbr\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'E:\\ruanjian\\Android2222\\jbr\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'E:\\ruanjian\\Android2222\\jbr\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'E:\\ruanjian\\Android2222\\jbr\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'E:\\ruanjian\\Android2222\\jbr\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'E:\\ruanjian\\Android2222\\jbr\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'E:\\ruanjian\\Android2222\\jbr\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'E:\\ruanjian\\Android2222\\jbr\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'E:\\ruanjian\\Android2222\\jbr\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'E:\\ruanjian\\Android2222\\jbr\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'E:\\ruanjian\\Android2222\\jbr\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'E:\\ruanjian\\Android2222\\jbr\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'E:\\ruanjian\\Android2222\\jbr\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'E:\\ruanjian\\Android2222\\jbr\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'E:\\ruanjian\\Android2222\\jbr\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'E:\\ruanjian\\Android2222\\jbr\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'E:\\ruanjian\\Android2222\\jbr\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'E:\\ruanjian\\Android2222\\jbr\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('_tcl_data\\tzdata\\Poland',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yakutsk',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Monaco',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\HST10',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baku',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Jan_Mayen',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Noumea',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('_tk_data\\ttk\\winTheme.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Stanley',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('_tk_data\\ttk\\vistaTheme.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp861.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Antananarivo',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo.eps',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Victoria',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\South_Georgia',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Windhoek',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('_tcl_data\\msgs\\ar.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('_tcl_data\\msgs\\tr.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Canberra',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('_tcl_data\\encoding\\cp737.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('_tcl_data\\msgs\\hi_in.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-7.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\East-Saskatchewan',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Canada\\East-Saskatchewan',
   'DATA'),
  ('_tcl_data\\tzdata\\Arctic\\Longyearbyen',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Lucia',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Syowa',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Mountain',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boa_Vista',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('_tcl_data\\tzdata\\W-SU',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Jujuy',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Yap',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Rangoon',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('_tcl_data\\tzdata\\Iran',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('_tcl_data\\msgs\\da.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fa.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vienna',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Matamoros',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ouagadougou',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ust-Nera',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('_tcl_data\\msgs\\eu_es.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1251.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Winamac',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Manila',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\ACT',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Magadan',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Shanghai',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('_tcl_data\\encoding\\cp1250.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-3.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Beulah',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo175.gif',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kirov',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('_tcl_data\\msgs\\en_ie.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Newfoundland',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Colombo',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('_tcl_data\\safe.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\safe.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ru.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tk_data\\msgs\\ru.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Galapagos',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Curacao',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Urumqi',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('_tcl_data\\msgs\\bn_in.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sarajevo',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Louisville',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('_tcl_data\\msgs\\es_pa.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Tucuman',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('_tk_data\\listbox.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\listbox.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\hu.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Isle_of_Man',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Makassar',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Cairo',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Almaty',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Saigon',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Timbuktu',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('_tcl_data\\msgs\\kok_in.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boise',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Petersburg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('_tcl_data\\msgs\\mk.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lord_Howe',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ojinaga',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimbu',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Eastern',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dakar',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('_tcl_data\\msgs\\eu.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Efate',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Majuro',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('_tcl_data\\encoding\\euc-jp.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('_tcl_data\\msgs\\cs.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Algiers',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Managua',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulaanbaatar',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Scoresbysund',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('_tcl_data\\encoding\\euc-kr.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('_tk_data\\spinbox.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Edmonton',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('_tcl_data\\encoding\\cp1254.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp936.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('_tcl_data\\encoding\\shiftjis.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia_Banderas',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('_tcl_data\\msgs\\en_nz.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('_tk_data\\images\\logo64.gif',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Damascus',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('_tcl_data\\encoding\\symbol.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Buenos_Aires',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Troll',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Harbin',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dili',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Djibouti',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yellowknife',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('_tcl_data\\msgs\\es_uy.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-r.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mayotte',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yangon',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('_tcl_data\\encoding\\dingbats.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Gibraltar',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('_tcl_data\\msgs\\fr.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Barnaul',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-15.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('_tcl_data\\msgs\\mt.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.3.tm',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8\\8.5\\tcltest-2.5.3.tm',
   'DATA'),
  ('_tk_data\\console.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\console.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Atyrau',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('_tcl_data\\tzdata\\UCT',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('_tk_data\\images\\logoLarge.gif',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anchorage',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Chagos',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vilnius',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Apia',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Douala',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\Continental',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('_tcl_data\\tm.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tm.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yerevan',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zagreb',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('_tcl_data\\msgs\\es_cr.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Rothera',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('_tk_data\\ttk\\fonts.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\id.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macUkraine.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('_tk_data\\ttk\\altTheme.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('_tk_data\\license.terms',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Kerguelen',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Danmarkshavn',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kashgar',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Sakhalin',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kosrae',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Coral_Harbour',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Antigua',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Tell_City',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-kr.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Gambier',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tashkent',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('_tk_data\\ttk\\clamTheme.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT0',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Halifax',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dominica',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lower_Princes',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Miquelon',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mogadishu',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('_tcl_data\\msgs\\eo.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Jamaica',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\Israel',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\DeNoronha',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montserrat',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ulyanovsk',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Samoa',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('_tcl_data\\msgs\\es_ve.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp857.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Menominee',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tongatapu',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('_tk_data\\scrlbar.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Khandyga',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Choibalsan',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mauritius',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('_tcl_data\\msgs\\zh.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hebron',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('_tk_data\\mkpsenc.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('_tk_data\\panedwindow.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ar_jo.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macRomania.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jakarta',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guadeloupe',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ujung_Pandang',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Banjul',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('_tcl_data\\msgs\\en_ca.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Indiana-Starke',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('_tk_data\\msgs\\en.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Juneau',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Recife',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('_tcl_data\\msgs\\kl_gl.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Aruba',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('_tk_data\\ttk\\treeview.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Godthab',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Mariehamn',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('_tcl_data\\msgs\\id_id.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dushanbe',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('_tcl_data\\encoding\\cp949.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp437.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashgabat',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Skopje',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tomsk',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Zulu',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pitcairn',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\LHI',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Freetown',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Nicosia',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jujuy',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Metlakatla',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('_tk_data\\ttk\\scale.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macau',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('_tcl_data\\msgs\\en_ph.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guatemala',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('_tcl_data\\tzdata\\EET',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\EET',
   'DATA'),
  ('_tcl_data\\opt0.4\\pkgIndex.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bahrain',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('_tcl_data\\msgs\\ta_in.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Asuncion',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tortola',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-7',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kaliningrad',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Astrakhan',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maseru',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+5',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('_tcl_data\\msgs\\ar_in.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Luxembourg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('_tcl_data\\msgs\\lt.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Nicosia',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('_tk_data\\tclIndex',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Funafuti',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Guernsey',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Creston',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bangui',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9YDT',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('_tcl_data\\tzdata\\CST6CDT',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tokyo',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Monrovia',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atka',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ceuta',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pyongyang',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Blanc-Sablon',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('_tcl_data\\msgs\\ga.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('_tcl_data\\msgs\\nn.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Norfolk',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grand_Turk',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kigali',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('_tcl_data\\encoding\\macRoman.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Libreville',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\East',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('_tcl_data\\tzdata\\Greenwich',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-11',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('_tcl_data\\msgs\\uk.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Calcutta',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Khartoum',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Winnipeg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belfast',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaSur',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('_tcl_data\\msgs\\es_co.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Reunion',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Martinique',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('_tcl_data\\encoding\\macThai.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kiev',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lome',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312-raw.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\WET',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\WET',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Barbados',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('_tcl_data\\msgs\\vi.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Hobart',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('_tk_data\\focus.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\focus.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Egypt',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Los_Angeles',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('_tk_data\\ttk\\scrollbar.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-5.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp860.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grenada',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('_tk_data\\comdlg.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\San_Marino',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('_tcl_data\\encoding\\macDingbats.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('_tcl_data\\msgs\\de.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('_tk_data\\ttk\\button.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('_tk_data\\obsolete.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('_tk_data\\msgbox.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Greenwich',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Chisinau',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('_tcl_data\\msgs\\sr.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('_tcl_data\\http1.0\\http.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\gv.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kiritimati',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Uzhgorod',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\El_Aaiun',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('_tcl_data\\encoding\\cp1258.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Eirunepe',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('_tcl_data\\encoding\\jis0208.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Bougainville',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chungking',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-12',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6CDT',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('_tcl_data\\msgs\\ta.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('_tcl_data\\msgs\\it.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rankin_Inlet',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('_tcl_data\\msgs\\en_gb.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Goose_Bay',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faeroe',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Podgorica',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Vancouver',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Indianapolis',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('_tcl_data\\msgs\\es_sv.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\MET',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\MET',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chuuk',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-10.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Addis_Ababa',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Lisbon',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Anadyr',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Araguaina',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Mendoza',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Copenhagen',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('_tcl_data\\msgs\\th.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\London',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Singapore',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bangkok',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('_tcl_data\\tzdata\\CET',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\CET',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Johannesburg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('_tk_data\\button.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UTC',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('_tcl_data\\encoding\\cns11643.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\cns11643.enc',
   'DATA'),
  ('_tk_data\\msgs\\pt.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\EST5EDT',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('_tk_data\\ttk\\ttk.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp863.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aden',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('_tcl_data\\encoding\\cp775.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Melbourne',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Inuvik',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lusaka',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('_tcl_data\\tzdata\\PST8PDT',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atikokan',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Kitts',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('_tcl_data\\msgs\\nl_be.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Oslo',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Puerto_Rico',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Brazzaville',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-13.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Campo_Grande',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macao',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('_tk_data\\ttk\\classicTheme.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novosibirsk',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ca.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp866.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pago_Pago',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Acre',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('_tcl_data\\msgs\\es_ar.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('_tk_data\\images\\tai-ku.gif',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('_tcl_data\\msgs\\bn.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Johnston',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Auckland',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('_tk_data\\msgs\\es.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT-0',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-4',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('_tcl_data\\msgs\\fa_in.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Riga',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('_tcl_data\\msgs\\zh_sg.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_in.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Honolulu',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Warsaw',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-4.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+4',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Knox_IN',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('_tcl_data\\msgs\\be.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('_tcl_data\\msgs\\et.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('_tcl_data\\msgs\\kw.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Harare',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tahiti',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Istanbul',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jerusalem',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santa_Isabel',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Samarkand',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('_tcl_data\\msgs\\ko_kr.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashkhabad',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('_tcl_data\\encoding\\cp874.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Glace_Bay',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('_tcl_data\\msgs\\sv.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_pr.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Enderbury',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tiraspol',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('_tcl_data\\msgs\\en_za.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Atlantic',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chita',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hovd',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT+0',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pontianak',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Gaza',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('_tcl_data\\msgs\\ja.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('_tcl_data\\msgs\\pt.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ-CHAT',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Knox',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Hermosillo',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('_tcl_data\\tzdata\\Kwajalein',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('_tcl_data\\init.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\init.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\pl.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bogota',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Manaus',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo200.gif',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Comoro',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Athens',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tarawa',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Brussels',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-8',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tripoli',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('_tcl_data\\tzdata\\ROC',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('_tcl_data\\msgs\\es_ec.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fo_fo.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\St_Helena',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guyana',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Conakry',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('_tcl_data\\encoding\\macTurkish.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('_tk_data\\pkgIndex.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Amsterdam',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('_tcl_data\\tzdata\\EST',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\EST',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Marigot',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('_tk_data\\icons.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\icons.tcl',
   'DATA'),
  ('_tk_data\\images\\logo.eps',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Mawson',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Marquesas',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4ADT',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('_tcl_data\\encoding\\tis-620.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bishkek',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('_tk_data\\ttk\\combobox.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Gaborone',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo75.gif',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fakaofo',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\La_Rioja',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Prague',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('_tcl_data\\msgs\\it_ch.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-5',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vladivostok',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Toronto',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('_tcl_data\\encoding\\cp1252.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('_tcl_data\\clock.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\clock.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Rome',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('_tcl_data\\tclIndex',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Havana',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guadalcanal',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Omsk',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Davis',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('_tcl_data\\msgs\\ar_lb.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chicago',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('_tcl_data\\msgs\\pt_br.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\PRC',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mendoza',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('_tcl_data\\msgs\\en_bw.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('_tk_data\\tk.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\tk.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp932.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Andorra',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\New_York',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Muscat',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rio_Branco',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Karachi',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('_tcl_data\\msgs\\nl.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sh.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Seoul',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-8.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Midway',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Niamey',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Saskatchewan',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmera',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('_tk_data\\ttk\\sizegrip.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Eucla',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('_tcl_data\\msgs\\fo.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Malta',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('_tcl_data\\http1.0\\pkgIndex.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\gb1988.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Ushuaia',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('_tcl_data\\msgs\\ar_sy.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kinshasa',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('_tcl_data\\tzdata\\Portugal',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Broken_Hill',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('_tk_data\\ttk\\spinbox.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuching',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('_tcl_data\\msgs\\mr_in.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('_tk_data\\fontchooser.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\North',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('_tcl_data\\tzdata\\HST',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\HST',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Moscow',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Christmas',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('_tcl_data\\tzdata\\GB',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\GB',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vevay',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vatican',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ljubljana',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('_tk_data\\ttk\\defaults.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montreal',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('_tcl_data\\msgs\\af.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belem',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('_tk_data\\msgs\\sv.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macIceland.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmara',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santo_Domingo',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Yancowinna',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Detroit',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rosario',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\Acre',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Denver',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Aleutian',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('_tcl_data\\msgs\\sq.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('_tk_data\\megawidget.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\GB-Eire',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('_tcl_data\\encoding\\cp862.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Mountain',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lagos',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Sao_Tome',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Queensland',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\La_Paz',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('_tcl_data\\encoding\\cp852.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-2',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Krasnoyarsk',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+1',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('_tk_data\\entry.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\entry.tcl',
   'DATA'),
  ('_tk_data\\msgs\\en_gb.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Samara',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('_tk_data\\bgerror.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\sw.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('_tk_data\\ttk\\menubutton.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('_tk_data\\msgs\\de.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Pacific',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Resolute',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yakutat',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bamako',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Palau',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('_tcl_data\\msgs\\kw_gb.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+2',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('_tk_data\\images\\README',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fortaleza',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-1.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indianapolis',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('_tk_data\\text.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\text.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Cuba',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Famagusta',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Brunei',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Phoenix',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Thomas',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('_tcl_data\\msgs\\hi.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-2.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-3',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montevideo',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+10',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('_tk_data\\safetk.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\safetk.tcl',
   'DATA'),
  ('_tcl_data\\auto.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\auto.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guam',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\EasterIsland',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('_tcl_data\\msgs\\ca.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Helsinki',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('_tcl_data\\msgs\\he.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-16.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('_tk_data\\ttk\\panedwindow.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Eastern',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Saipan',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bujumbura',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Wayne',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('_tcl_data\\encoding\\ascii.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Dublin',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('_tcl_data\\msgs\\ms_my.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('_tk_data\\msgs\\nl.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Budapest',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dhaka',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Vostok',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Michigan',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('_tcl_data\\msgs\\es_gt.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Nauru',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('_tcl_data\\encoding\\cp1253.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('_tk_data\\ttk\\utils.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_cl.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Porto-Novo',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('_tcl_data\\encoding\\cp1257.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Oral',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Ponape',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Darwin',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('_tcl_data\\msgs\\af_za.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dubai',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('_tcl_data\\parray.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\parray.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cancun',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Swift_Current',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Accra',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('_tk_data\\msgs\\el.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+12',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('_tk_data\\ttk\\progress.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zaporozhye',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtau',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('_tcl_data\\encoding\\jis0212.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-14.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_py.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ga_ie.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_mx.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vaduz',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Paramaribo',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+0',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Barthelemy',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('_tcl_data\\encoding\\cp850.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santarem',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tallinn',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chongqing',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vientiane',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaNorte',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nome',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kwajalein',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Merida',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-9',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('_tcl_data\\word.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\word.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Regina',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Monticello',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Amman',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\ComodRivadavia',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ch.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('_tcl_data\\msgs\\de_be.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Adak',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Whitehorse',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('_tcl_data\\msgs\\zh_hk.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Punta_Arenas',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('_tk_data\\optMenu.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson_Creek',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Taipei',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('_tcl_data\\tzdata\\Navajo',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Central',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulan_Bator',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('_tcl_data\\msgs\\te.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fr_be.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Reykjavik',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Louisville',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\West',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ho_Chi_Minh',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kampala',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\McMurdo',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qyzylorda',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimphu',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santiago',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('_tcl_data\\encoding\\cp855.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tirane',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('_tk_data\\unsupported.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kanton',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Pacific\\Kanton',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Currie',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Easter',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kralendijk',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo150.gif',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Saratov',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Caracas',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Blantyre',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.18.tm',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8\\8.4\\platform-1.0.18.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Busingen',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Moncton',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('_tcl_data\\encoding\\cp865.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('_tk_data\\msgs\\hu.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Katmandu',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Catamarca',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\Hongkong',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Tasmania',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('_tcl_data\\encoding\\cp869.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kamchatka',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Jersey',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('_tcl_data\\encoding\\macCroatian.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp864.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Zulu',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('_tcl_data\\msgs\\kok.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8PDT',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Phnom_Penh',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Madeira',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-10',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('_tcl_data\\msgs\\bg.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bratislava',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('_tcl_data\\tzdata\\Turkey',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7MDT',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Panama',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('_tcl_data\\tzdata\\Iceland',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('_tcl_data\\msgs\\es_ni.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Adelaide',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('_tcl_data\\msgs\\hr.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tijuana',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wallis',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('_tcl_data\\tzdata\\Universal',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+9',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('_tcl_data\\package.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\package.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\UTC',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mazatlan',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('_tcl_data\\msgs\\te_in.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('_tk_data\\xmfbox.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baghdad',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lindeman',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('_tcl_data\\tzdata\\Japan',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Buenos_Aires',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\El_Salvador',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Berlin',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('_tk_data\\images\\logo100.gif',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sitka',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('_tcl_data\\encoding\\macCentEuro.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Luis',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thule',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('_tcl_data\\tzdata\\Libya',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kathmandu',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayenne',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Arizona',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('_tcl_data\\encoding\\macCyrillic.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port-au-Prince',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sofia',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Sydney',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tehran',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Beirut',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('_tcl_data\\msgs\\zh_cn.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tk_data\\menu.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\menu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lima',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('_tcl_data\\msgs\\es_hn.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('_tk_data\\msgs\\da.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('_tk_data\\images\\logoMed.gif',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Pangnirtung',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\NSW',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('_tcl_data\\encoding\\macJapan.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Maceio',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('_tcl_data\\msgs\\en_zw.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5EDT',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('_tcl_data\\msgs\\zh_tw.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Monterrey',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuwait',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Salta',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('_tcl_data\\msgs\\en_au.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fi.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\DumontDUrville',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\West',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nuuk',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Nuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Catamarca',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Shiprock',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Center',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Cordoba',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('_tcl_data\\encoding\\big5.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('_tcl_data\\encoding\\euc-cn.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bucharest',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Paris',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+3',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-0',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tel_Aviv',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific-New',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\US\\Pacific-New',
   'DATA'),
  ('_tcl_data\\tzdata\\Singapore',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuala_Lumpur',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('_tk_data\\ttk\\xpTheme.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\South',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('_tcl_data\\msgs\\kl.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-13',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qatar',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('_tcl_data\\tzdata\\ROK',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bissau',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('_tk_data\\choosedir.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\gl_es.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lubumbashi',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mahe',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Azores',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Macquarie',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anguilla',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('_tcl_data\\msgs\\nb.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('_tk_data\\msgs\\cs.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Cocos',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ndjamena',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtobe',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Casey',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qostanay',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Qostanay',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wake',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cordoba',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Marengo',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Perth',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mexico_City',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Port_Moresby',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Juan',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('_tcl_data\\msgs\\ms.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Irkutsk',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('_tcl_data\\msgs\\en_sg.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-9.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jayapura',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('_tcl_data\\msgs\\fa_ir.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cuiaba',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('_tcl_data\\msgs\\ko.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Minsk',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nairobi',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\General',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novokuznetsk',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('_tcl_data\\msgs\\sk.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Stockholm',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mbabane',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('_tcl_data\\msgs\\mr.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Volgograd',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('_tcl_data\\tzdata\\MST',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\MST',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tbilisi',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('_tk_data\\ttk\\entry.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maputo',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('_tcl_data\\msgs\\lv.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nipigon',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yekaterinburg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-u.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\NZ',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faroe',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Cape_Verde',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('_tcl_data\\msgs\\en_hk.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zurich',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Simferopol',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('_tcl_data\\msgs\\is.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-14',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belize',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-6.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\East-Indiana',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+6',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Samoa',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('_tcl_data\\msgs\\de_at.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Vincent',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('_tk_data\\iconlist.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nassau',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('_tk_data\\ttk\\aquaTheme.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cambridge_Bay',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UCT',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('_tcl_data\\encoding\\cp1255.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Alaska',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.5.tm',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8\\8.6\\http-2.9.5.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Rarotonga',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Istanbul',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Virgin',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pohnpei',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Nelson',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Noronha',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rainy_River',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Johns',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('_tcl_data\\msgs\\es_bo.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('_tcl_data\\encoding\\ksc5601.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp950.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dar_es_Salaam',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Yukon',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Brisbane',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Central',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tegucigalpa',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('_tk_data\\dialog.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\dialog.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Eire',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\MST7MDT',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('_tcl_data\\msgs\\es_do.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Madrid',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('_tcl_data\\encoding\\jis0201.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-jp.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('_tk_data\\msgs\\fr.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Srednekolymsk',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Palmer',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vincennes',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belgrade',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('_tcl_data\\msgs\\ro.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Luanda',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('_tk_data\\msgs\\it.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Velho',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\New_Salem',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Niue',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('_tk_data\\clrpick.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+7',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('_tcl_data\\encoding\\macGreek.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Bermuda',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ensenada',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('_tk_data\\tkfbox.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp1256.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayman',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Casablanca',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Canary',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Iqaluit',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Riyadh',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nouakchott',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\South_Pole',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guayaquil',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('_tk_data\\ttk\\cursors.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sao_Paulo',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Universal',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('_tcl_data\\msgs\\es_pe.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('_tk_data\\ttk\\notebook.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Maldives',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('_tk_data\\palette.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\palette.tcl',
   'DATA'),
  ('_tk_data\\msgs\\eo.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kolkata',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Abidjan',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('_tk_data\\msgs\\pl.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thunder_Bay',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('_tcl_data\\msgs\\gl.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fiji',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-1',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chatham',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('_tcl_data\\msgs\\gv_gb.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ru_ua.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-11.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\iso8859-11.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dacca',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('_tcl_data\\opt0.4\\optparse.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+11',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Costa_Rica',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Juba',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Truk',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('_tk_data\\tearoff.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tunis',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hong_Kong',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Malabo',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('_tcl_data\\encoding\\gb12345.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('_tcl_data\\msgs\\en_be.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('_tcl_data\\history.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\history.tcl',
   'DATA'),
  ('_tk_data\\scale.tcl',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\scale.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Hawaii',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+8',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('_tcl_data\\msgs\\sl.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo100.gif',
   'D:\\huanjing\\Python3.11.2\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kabul',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jamaica',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('_tcl_data\\encoding\\ebcdic.enc',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-6',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('_tcl_data\\msgs\\el.msg',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chihuahua',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT0',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port_of_Spain',
   'D:\\huanjing\\Python3.11.2\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'DATA'),
  ('numpy-2.2.4.dist-info\\METADATA',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy-2.2.4.dist-info\\METADATA',
   'DATA'),
  ('numpy-2.2.4.dist-info\\DELVEWHEEL',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy-2.2.4.dist-info\\DELVEWHEEL',
   'DATA'),
  ('numpy-2.2.4.dist-info\\INSTALLER',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy-2.2.4.dist-info\\INSTALLER',
   'DATA'),
  ('numpy-2.2.4.dist-info\\WHEEL',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy-2.2.4.dist-info\\WHEEL',
   'DATA'),
  ('numpy-2.2.4.dist-info\\LICENSE.txt',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy-2.2.4.dist-info\\LICENSE.txt',
   'DATA'),
  ('numpy-2.2.4.dist-info\\entry_points.txt',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy-2.2.4.dist-info\\entry_points.txt',
   'DATA'),
  ('numpy-2.2.4.dist-info\\RECORD',
   'D:\\huanjing\\Python3.11.2\\Lib\\site-packages\\numpy-2.2.4.dist-info\\RECORD',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\公司脚本\\weicheng\\build\\Walmart数据合并工具\\base_library.zip',
   'DATA')],
 'python311.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
